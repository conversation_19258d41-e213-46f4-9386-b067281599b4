{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@prisma/client": "^5.7.0", "prisma": "^5.7.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "zod": "^3.22.0", "redis": "^4.6.0", "bullmq": "^5.0.0", "node-cron": "^3.0.3", "resend": "^2.1.0", "react-email": "^2.0.0", "openai": "^4.20.0", "axios": "^1.6.0", "google-trends-api": "^4.9.2", "cheerio": "^1.0.0-rc.12", "date-fns": "^2.30.0", "framer-motion": "^10.16.0", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.4.0", "@tanstack/react-query": "^5.8.0", "zustand": "^4.4.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "recharts": "^2.8.0", "pino": "^8.17.0", "pino-pretty": "^10.3.0", "helmet": "^7.1.0", "express-rate-limit": "^7.1.0", "next-auth": "^4.24.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "class-variance-authority": "^0.7.0", "next-themes": "^0.2.1"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node-cron": "^3.0.11", "typescript": "^5.3.0", "tailwindcss": "^3.3.0", "postcss": "^8.4.32", "autoprefixer": "^10.4.16", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10"}}