// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  password      String?
  timezone      String    @default("UTC")
  emailEnabled  Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // OAuth fields
  googleId      String?   @unique
  githubId      String?   @unique
  
  // Relations
  keywords      Keyword[]
  reports       Report[]
  notifications Notification[]
  
  @@map("users")
}

model Keyword {
  id          String    @id @default(cuid())
  term        String
  isActive    Boolean   @default(true)
  sortOrder   Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  // Foreign keys
  userId      String
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Relations
  reports     Report[]
  trends      TrendData[]
  
  @@unique([userId, term])
  @@map("keywords")
}

model Report {
  id            String    @id @default(cuid())
  date          DateTime  @default(now())
  content       Json
  emailSent     Boolean   @default(false)
  emailSentAt   DateTime?
  generatedAt   DateTime  @default(now())
  
  // Foreign keys
  userId        String
  keywordId     String
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  keyword       Keyword   @relation(fields: [keywordId], references: [id], onDelete: Cascade)
  
  @@unique([userId, keywordId, date])
  @@map("reports")
}

model TrendData {
  id            String    @id @default(cuid())
  date          DateTime  @default(now())
  hotness       Float     @default(0)
  searchVolume  Int       @default(0)
  newsCount     Int       @default(0)
  socialMentions Int      @default(0)
  sentiment     Float     @default(0) // -1 to 1
  rawData       Json
  
  // Foreign keys
  keywordId     String
  keyword       Keyword   @relation(fields: [keywordId], references: [id], onDelete: Cascade)
  
  @@unique([keywordId, date])
  @@map("trend_data")
}

model Notification {
  id            String    @id @default(cuid())
  type          String    // "success", "error", "info", "warning"
  title         String
  message       String
  isRead        Boolean   @default(false)
  createdAt     DateTime  @default(now())
  
  // Foreign keys
  userId        String
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("notifications")
}

model JobLog {
  id            String    @id @default(cuid())
  jobType       String    // "daily_report", "trend_fetch", etc.
  status        String    // "pending", "running", "completed", "failed"
  startedAt     DateTime  @default(now())
  completedAt   DateTime?
  error         String?
  metadata      Json?
  
  @@map("job_logs")
}

model SystemConfig {
  id            String    @id @default(cuid())
  key           String    @unique
  value         String
  updatedAt     DateTime  @updatedAt
  
  @@map("system_config")
}
