import { Resend } from 'resend';
import { DailyReportEmail } from '../../../emails/templates/daily-report';
import { WelcomeEmail } from '../../../emails/templates/welcome';

const resend = new Resend(process.env.RESEND_API_KEY);

export interface EmailData {
  to: string;
  subject: string;
  html?: string;
  react?: React.ReactElement;
}

export async function sendEmail(data: EmailData): Promise<boolean> {
  try {
    const result = await resend.emails.send({
      from: process.env.FROM_EMAIL || 'TrendPulse <<EMAIL>>',
      to: data.to,
      subject: data.subject,
      html: data.html,
      react: data.react,
    });
    
    return !!result.data;
  } catch (error) {
    console.error('Email sending failed:', error);
    return false;
  }
}

export async function sendWelcomeEmail(to: string, name: string): Promise<boolean> {
  return sendEmail({
    to,
    subject: 'Welcome to TrendPulse!',
    react: WelcomeEmail({ name }),
  });
}

export async function sendDailyReport(
  to: string,
  userName: string,
  keyword: string,
  reportData: any
): Promise<boolean> {
  return sendEmail({
    to,
    subject: `Daily Trend Report: ${keyword}`,
    react: DailyReportEmail({
      userName,
      keyword,
      reportData,
    }),
  });
}

export async function sendBulkDailyReports(
  to: string,
  userName: string,
  reports: Array<{ keyword: string; data: any }>
): Promise<boolean> {
  const htmlContent = reports.map(report => 
    `<div style="margin-bottom: 40px;">
      <h2 style="color: #4f46e5; margin-bottom: 20px;">${report.keyword}</h2>
      ${generateReportHTML(report.data)}
    </div>`
  ).join('');

  return sendEmail({
    to,
    subject: `Your Daily Trend Reports - ${new Date().toLocaleDateString()}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #333; text-align: center; margin-bottom: 30px;">
          Daily Trend Reports
        </h1>
        <p style="color: #666; margin-bottom: 30px;">
          Hi ${userName}, here are your daily trend reports:
        </p>
        ${htmlContent}
        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee;">
          <p style="color: #999; font-size: 14px;">
            This email was sent by TrendPulse. 
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/settings" style="color: #4f46e5;">
              Manage your preferences
            </a>
          </p>
        </div>
      </div>
    `,
  });
}

function generateReportHTML(data: any): string {
  return `
    <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
      <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
        <div>
          <h3 style="margin: 0; color: #1e293b;">Hotness Score</h3>
          <p style="margin: 5px 0; font-size: 24px; font-weight: bold; color: #4f46e5;">
            ${data.hotness || 0}/100
          </p>
        </div>
        <div>
          <h3 style="margin: 0; color: #1e293b;">Sentiment</h3>
          <p style="margin: 5px 0; font-size: 18px; color: ${data.sentiment > 0 ? '#22c55e' : data.sentiment < 0 ? '#ef4444' : '#6b7280'};">
            ${data.sentiment > 0 ? '📈 Positive' : data.sentiment < 0 ? '📉 Negative' : '➡️ Neutral'}
          </p>
        </div>
      </div>
      
      <div style="margin-bottom: 20px;">
        <h3 style="margin: 0 0 10px 0; color: #1e293b;">Key Insights</h3>
        <p style="margin: 0; color: #475569; line-height: 1.6;">
          ${data.insights || 'No insights available for this period.'}
        </p>
      </div>
      
      <div style="margin-bottom: 20px;">
        <h3 style="margin: 0 0 10px 0; color: #1e293b;">Top News</h3>
        ${data.news ? data.news.slice(0, 3).map((item: any) => `
          <div style="margin-bottom: 10px; padding: 10px; background: white; border-radius: 4px;">
            <h4 style="margin: 0 0 5px 0; color: #4f46e5; font-size: 14px;">
              <a href="${item.url}" style="color: #4f46e5; text-decoration: none;">
                ${item.title}
              </a>
            </h4>
            <p style="margin: 0; color: #64748b; font-size: 12px; line-height: 1.4;">
              ${item.summary}
            </p>
          </div>
        `).join('') : '<p style="color: #94a3b8;">No news available</p>'}
      </div>
    </div>
  `;
}
