import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Providers } from './providers';
import { Toaster } from 'react-hot-toast';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'TrendPulse - Daily Trend Analysis Reports',
  description: 'Get personalized daily trend reports for your keywords. Stay ahead of the curve with AI-powered insights.',
  keywords: ['trends', 'analysis', 'reports', 'AI', 'keywords', 'news'],
  authors: [{ name: 'TrendPulse Team' }],
  creator: 'TrendPulse',
  openGraph: {
    title: 'TrendPulse - Daily Trend Analysis Reports',
    description: 'Get personalized daily trend reports for your keywords. Stay ahead of the curve with AI-powered insights.',
    url: 'https://trendpulse.com',
    siteName: 'TrendPulse',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'TrendPulse Dashboard',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TrendPulse - Daily Trend Analysis Reports',
    description: 'Get personalized daily trend reports for your keywords. Stay ahead of the curve with AI-powered insights.',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
          <Toaster 
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'hsl(var(--background))',
                color: 'hsl(var(--foreground))',
                border: '1px solid hsl(var(--border))',
              },
            }}
          />
        </Providers>
      </body>
    </html>
  );
}
