import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { requireAuth } from '@/lib/auth';
import { z } from 'zod';

const createKeywordSchema = z.object({
  term: z.string().min(1).max(100),
});

export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth(request);
    
    const keywords = await db.keyword.findMany({
      where: { userId: user.id },
      include: {
        trends: {
          orderBy: { date: 'desc' },
          take: 1,
        },
        _count: {
          select: { reports: true },
        },
      },
      orderBy: { sortOrder: 'asc' },
    });

    return NextResponse.json(keywords);
  } catch (error) {
    console.error('Error fetching keywords:', error);
    return NextResponse.json(
      { error: 'Failed to fetch keywords' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth(request);
    const body = await request.json();
    
    const { term } = createKeywordSchema.parse(body);
    
    // Check if keyword already exists for this user
    const existingKeyword = await db.keyword.findUnique({
      where: {
        userId_term: {
          userId: user.id,
          term: term.toLowerCase(),
        },
      },
    });

    if (existingKeyword) {
      return NextResponse.json(
        { error: 'Keyword already exists' },
        { status: 400 }
      );
    }

    // Get max sort order
    const maxSortOrder = await db.keyword.findFirst({
      where: { userId: user.id },
      orderBy: { sortOrder: 'desc' },
      select: { sortOrder: true },
    });

    const keyword = await db.keyword.create({
      data: {
        term: term.toLowerCase(),
        userId: user.id,
        sortOrder: (maxSortOrder?.sortOrder || 0) + 1,
      },
    });

    // Schedule initial trend fetch
    // TODO: Add to job queue

    return NextResponse.json(keyword, { status: 201 });
  } catch (error) {
    console.error('Error creating keyword:', error);
    return NextResponse.json(
      { error: 'Failed to create keyword' },
      { status: 500 }
    );
  }
}
