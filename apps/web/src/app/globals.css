@tailwind base;
@tailwind components;
@tailwind utilities;

/* 简化的CSS变量 */
:root {
  --primary-color: #4f46e5;
  --secondary-color: #22d3ee;
}

body {
  font-feature-settings: "rlig" 1, "calt" 1;
}

@layer base {
  h1 {
    @apply scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl;
  }
  
  h2 {
    @apply scroll-m-20 border-b pb-2 text-3xl font-semibold tracking-tight first:mt-0;
  }
  
  h3 {
    @apply scroll-m-20 text-2xl font-semibold tracking-tight;
  }
  
  h4 {
    @apply scroll-m-20 text-xl font-semibold tracking-tight;
  }
  
  p {
    @apply leading-7 [&:not(:first-child)]:mt-6;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }
  
  .btn-primary {
    @apply bg-purple-600 text-white hover:bg-purple-700;
  }
  
  .btn-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200;
  }
  
  .btn-ghost {
    @apply hover:bg-gray-100 hover:text-gray-900;
  }
  
  .btn-sm {
    @apply h-9 rounded-md px-3;
  }
  
  .btn-lg {
    @apply h-11 rounded-md px-8;
  }
  
  .card {
    @apply rounded-lg border bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-purple-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
}

.glass {
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(255, 255, 255, 0.75);
  border: 1px solid rgba(209, 213, 219, 0.3);
}

.dark .glass {
  background-color: rgba(17, 24, 39, 0.75);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dark .gradient-bg {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f3f4f6;
}

::-webkit-scrollbar-thumb {
  background: #9ca3af;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading {
  animation: spin 1s linear infinite;
}

/* Fade in animation */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
