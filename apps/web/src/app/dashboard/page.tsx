'use client';

import { useState } from 'react';
import { Header } from '@/components/Layout/Header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Modal } from '@/components/ui/Modal';
import { Input } from '@/components/ui/Input';
import { PlusIcon, TrendingUpIcon, EyeIcon, TrashIcon } from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';

interface Keyword {
  id: string;
  term: string;
  hotness: number;
  lastUpdate: string;
  trend: 'up' | 'down' | 'stable';
  isActive: boolean;
}

export default function Dashboard() {
  const [keywords, setKeywords] = useState<Keyword[]>([
    {
      id: '1',
      term: 'AI大模型',
      hotness: 85,
      lastUpdate: '2024-01-15',
      trend: 'up',
      isActive: true,
    },
    {
      id: '2',
      term: 'Web3',
      hotness: 65,
      lastUpdate: '2024-01-15',
      trend: 'down',
      isActive: true,
    },
    {
      id: '3',
      term: 'React 18',
      hotness: 72,
      lastUpdate: '2024-01-15',
      trend: 'stable',
      isActive: true,
    },
  ]);

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [newKeyword, setNewKeyword] = useState('');

  const handleAddKeyword = () => {
    if (newKeyword.trim()) {
      const newKeywordObj: Keyword = {
        id: Date.now().toString(),
        term: newKeyword.trim(),
        hotness: 0,
        lastUpdate: new Date().toISOString().split('T')[0],
        trend: 'stable',
        isActive: true,
      };
      setKeywords([...keywords, newKeywordObj]);
      setNewKeyword('');
      setIsAddModalOpen(false);
    }
  };

  const handleDeleteKeyword = (id: string) => {
    setKeywords(keywords.filter(k => k.id !== id));
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUpIcon className="h-4 w-4 text-green-500" />;
      case 'down':
        return <TrendingUpIcon className="h-4 w-4 text-red-500 rotate-180" />;
      default:
        return <div className="h-4 w-4 rounded-full bg-yellow-500" />;
    }
  };

  const getHotnessColor = (hotness: number) => {
    if (hotness >= 80) return 'text-red-500';
    if (hotness >= 60) return 'text-orange-500';
    if (hotness >= 40) return 'text-yellow-500';
    return 'text-green-500';
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-muted-foreground">
              Monitor and manage your keyword trends
            </p>
          </div>
          <Button onClick={() => setIsAddModalOpen(true)} className="gap-2">
            <PlusIcon className="h-4 w-4" />
            Add Keyword
          </Button>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Keywords</CardTitle>
              <div className="h-4 w-4 text-muted-foreground">📊</div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{keywords.length}</div>
              <p className="text-xs text-muted-foreground">
                +2 from last month
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Hotness</CardTitle>
              <div className="h-4 w-4 text-muted-foreground">🔥</div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(keywords.reduce((sum, k) => sum + k.hotness, 0) / keywords.length)}
              </div>
              <p className="text-xs text-muted-foreground">
                +5% from yesterday
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Trending Up</CardTitle>
              <div className="h-4 w-4 text-muted-foreground">📈</div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {keywords.filter(k => k.trend === 'up').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Out of {keywords.length} keywords
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Keywords Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {keywords.map((keyword, index) => (
            <motion.div
              key={keyword.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{keyword.term}</CardTitle>
                    <div className="flex items-center gap-2">
                      {getTrendIcon(keyword.trend)}
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteKeyword(keyword.id)}
                        className="h-8 w-8 text-muted-foreground hover:text-destructive"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Hotness Score</span>
                      <span className={`text-2xl font-bold ${getHotnessColor(keyword.hotness)}`}>
                        {keyword.hotness}
                      </span>
                    </div>
                    
                    <div className="w-full bg-secondary rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-500 ${
                          keyword.hotness >= 80 ? 'bg-red-500' :
                          keyword.hotness >= 60 ? 'bg-orange-500' :
                          keyword.hotness >= 40 ? 'bg-yellow-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${keyword.hotness}%` }}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Last Updated</span>
                      <span>{keyword.lastUpdate}</span>
                    </div>
                    
                    <Button variant="outline" className="w-full gap-2">
                      <EyeIcon className="h-4 w-4" />
                      View Report
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Add Keyword Modal */}
        <Modal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          title="Add New Keyword"
        >
          <div className="space-y-4">
            <Input
              label="Keyword"
              placeholder="Enter keyword to track (e.g., AI, Web3, React)"
              value={newKeyword}
              onChange={(e) => setNewKeyword(e.target.value)}
              helperText="Choose keywords that are relevant to your interests or business"
            />
            <div className="flex gap-2 justify-end">
              <Button variant="outline" onClick={() => setIsAddModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddKeyword} disabled={!newKeyword.trim()}>
                Add Keyword
              </Button>
            </div>
          </div>
        </Modal>
      </main>
    </div>
  );
}
