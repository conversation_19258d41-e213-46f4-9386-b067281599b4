import axios from 'axios';

export interface SocialData {
  mentionCount: number;
  sentiment: number; // -1 to 1
  topMentions: Array<{
    text: string;
    platform: string;
    url: string;
    timestamp: string;
  }>;
  platformBreakdown: {
    twitter: number;
    reddit: number;
    other: number;
  };
}

export class SocialService {
  private static instance: SocialService;
  private twitterBearerToken: string;
  private redditClientId: string;
  private redditClientSecret: string;

  constructor() {
    this.twitterBearerToken = process.env.TWITTER_BEARER_TOKEN || '';
    this.redditClientId = process.env.REDDIT_CLIENT_ID || '';
    this.redditClientSecret = process.env.REDDIT_CLIENT_SECRET || '';
  }

  static getInstance(): SocialService {
    if (!SocialService.instance) {
      SocialService.instance = new SocialService();
    }
    return SocialService.instance;
  }

  async getSocialData(keyword: string): Promise<SocialData> {
    try {
      const [twitterData, redditData] = await Promise.all([
        this.getTwitterData(keyword),
        this.getRedditData(keyword),
      ]);

      const allMentions = [...twitterData.mentions, ...redditData.mentions];
      const totalMentions = twitterData.count + redditData.count;
      
      // Calculate overall sentiment
      const sentimentSum = allMentions.reduce((sum, mention) => sum + (mention.sentiment || 0), 0);
      const avgSentiment = allMentions.length > 0 ? sentimentSum / allMentions.length : 0;

      return {
        mentionCount: totalMentions,
        sentiment: avgSentiment,
        topMentions: allMentions.slice(0, 10),
        platformBreakdown: {
          twitter: twitterData.count,
          reddit: redditData.count,
          other: 0,
        },
      };
    } catch (error) {
      console.error('Error fetching social data:', error);
      return {
        mentionCount: 0,
        sentiment: 0,
        topMentions: [],
        platformBreakdown: {
          twitter: 0,
          reddit: 0,
          other: 0,
        },
      };
    }
  }

  private async getTwitterData(keyword: string): Promise<{
    count: number;
    mentions: Array<{
      text: string;
      platform: string;
      url: string;
      timestamp: string;
      sentiment?: number;
    }>;
  }> {
    try {
      // Twitter API v2 recent search
      const response = await axios.get('https://api.twitter.com/2/tweets/search/recent', {
        headers: {
          'Authorization': `Bearer ${this.twitterBearerToken}`,
        },
        params: {
          query: keyword,
          max_results: 100,
          'tweet.fields': 'created_at,public_metrics,text',
          'user.fields': 'username',
          expansions: 'author_id',
        },
      });

      const tweets = response.data.data || [];
      const mentions = tweets.map((tweet: any) => ({
        text: tweet.text,
        platform: 'twitter',
        url: `https://twitter.com/i/status/${tweet.id}`,
        timestamp: tweet.created_at,
        sentiment: this.calculateSentiment(tweet.text),
      }));

      return {
        count: tweets.length,
        mentions,
      };
    } catch (error) {
      console.error('Error fetching Twitter data:', error);
      return { count: 0, mentions: [] };
    }
  }

  private async getRedditData(keyword: string): Promise<{
    count: number;
    mentions: Array<{
      text: string;
      platform: string;
      url: string;
      timestamp: string;
      sentiment?: number;
    }>;
  }> {
    try {
      // Reddit API search
      const response = await axios.get('https://www.reddit.com/search.json', {
        params: {
          q: keyword,
          limit: 100,
          sort: 'new',
          type: 'link',
        },
      });

      const posts = response.data.data.children || [];
      const mentions = posts.map((post: any) => ({
        text: post.data.title + ' ' + (post.data.selftext || ''),
        platform: 'reddit',
        url: `https://reddit.com${post.data.permalink}`,
        timestamp: new Date(post.data.created_utc * 1000).toISOString(),
        sentiment: this.calculateSentiment(post.data.title + ' ' + (post.data.selftext || '')),
      }));

      return {
        count: posts.length,
        mentions,
      };
    } catch (error) {
      console.error('Error fetching Reddit data:', error);
      return { count: 0, mentions: [] };
    }
  }

  private calculateSentiment(text: string): number {
    // Simple sentiment analysis - in production, use a proper sentiment analysis library
    const positiveWords = [
      'good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'positive', 
      'bullish', 'up', 'rise', 'growth', 'love', 'like', 'best', 'awesome',
      'excited', 'happy', 'pleased', 'impressed', 'breakthrough', 'innovation'
    ];
    
    const negativeWords = [
      'bad', 'terrible', 'awful', 'horrible', 'negative', 'bearish', 'down', 
      'fall', 'decline', 'drop', 'hate', 'worst', 'disappointed', 'sad',
      'angry', 'frustrated', 'problem', 'issue', 'bug', 'error', 'failed'
    ];

    const words = text.toLowerCase().split(/\W+/);
    let score = 0;

    words.forEach(word => {
      if (positiveWords.includes(word)) score += 1;
      if (negativeWords.includes(word)) score -= 1;
    });

    // Normalize to -1 to 1 range
    const normalizedScore = Math.max(-1, Math.min(1, score / Math.sqrt(words.length)));
    return normalizedScore;
  }

  async getSocialTrends(keyword: string, days: number = 7): Promise<Array<{
    date: string;
    mentions: number;
    sentiment: number;
  }>> {
    try {
      // This would typically involve fetching historical data
      // For now, return mock data
      const trends = [];
      const now = new Date();
      
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        
        trends.push({
          date: date.toISOString().split('T')[0],
          mentions: Math.floor(Math.random() * 100) + 10,
          sentiment: (Math.random() - 0.5) * 2, // -1 to 1
        });
      }
      
      return trends;
    } catch (error) {
      console.error('Error fetching social trends:', error);
      return [];
    }
  }
}
