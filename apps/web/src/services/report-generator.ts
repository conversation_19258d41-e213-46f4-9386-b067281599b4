import { TrendsService } from './trends';
import { NewsService } from './news';
import { SocialService } from './social';
import { db } from '@/lib/db';
import { sendDailyReport } from '@/lib/email';
import OpenAI from 'openai';

export interface ReportData {
  keyword: string;
  date: string;
  hotness: number;
  sentiment: number;
  insights: string;
  news: Array<{
    title: string;
    url: string;
    summary: string;
    publishedAt: string;
    source: string;
  }>;
  socialMentions: number;
  chartData: Array<{
    date: string;
    value: number;
  }>;
  recommendations: string[];
}

export class ReportGenerator {
  private static instance: ReportGenerator;
  private openai: OpenAI;
  private trendsService: TrendsService;
  private newsService: NewsService;
  private socialService: SocialService;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.trendsService = TrendsService.getInstance();
    this.newsService = NewsService.getInstance();
    this.socialService = SocialService.getInstance();
  }

  static getInstance(): ReportGenerator {
    if (!ReportGenerator.instance) {
      ReportGenerator.instance = new ReportGenerator();
    }
    return ReportGenerator.instance;
  }

  async generateReport(keywordId: string, userId: string): Promise<ReportData> {
    try {
      // Get keyword details
      const keyword = await db.keyword.findUnique({
        where: { id: keywordId },
        include: { user: true },
      });

      if (!keyword) {
        throw new Error('Keyword not found');
      }

      // Fetch all data in parallel
      const [trendData, newsArticles, socialData] = await Promise.all([
        this.trendsService.getTrendData(keyword.term),
        this.newsService.getNews(keyword.term, 10),
        this.socialService.getSocialData(keyword.term),
      ]);

      // Generate AI insights
      const insights = await this.generateInsights(
        keyword.term,
        trendData,
        newsArticles,
        socialData
      );

      // Generate recommendations
      const recommendations = await this.generateRecommendations(
        keyword.term,
        trendData,
        socialData
      );

      const reportData: ReportData = {
        keyword: keyword.term,
        date: new Date().toISOString().split('T')[0],
        hotness: trendData.hotness,
        sentiment: socialData.sentiment,
        insights,
        news: newsArticles.slice(0, 5),
        socialMentions: socialData.mentionCount,
        chartData: trendData.timelineData,
        recommendations,
      };

      // Save report to database
      await db.report.create({
        data: {
          userId,
          keywordId,
          content: reportData,
          date: new Date(),
        },
      });

      // Update trend data
      await db.trendData.upsert({
        where: {
          keywordId_date: {
            keywordId,
            date: new Date(),
          },
        },
        update: {
          hotness: trendData.hotness,
          searchVolume: trendData.searchVolume,
          newsCount: newsArticles.length,
          socialMentions: socialData.mentionCount,
          sentiment: socialData.sentiment,
          rawData: { trendData, newsArticles, socialData },
        },
        create: {
          keywordId,
          date: new Date(),
          hotness: trendData.hotness,
          searchVolume: trendData.searchVolume,
          newsCount: newsArticles.length,
          socialMentions: socialData.mentionCount,
          sentiment: socialData.sentiment,
          rawData: { trendData, newsArticles, socialData },
        },
      });

      return reportData;
    } catch (error) {
      console.error('Error generating report:', error);
      throw error;
    }
  }

  private async generateInsights(
    keyword: string,
    trendData: any,
    newsArticles: any[],
    socialData: any
  ): Promise<string> {
    try {
      const prompt = `
        Analyze the following data for the keyword "${keyword}" and provide 2-3 key insights:

        Trend Data:
        - Hotness Score: ${trendData.hotness}/100
        - Search Volume: ${trendData.searchVolume}
        - Recent Timeline: ${JSON.stringify(trendData.timelineData.slice(-7))}

        News Articles: ${newsArticles.length} articles found
        Top Headlines: ${newsArticles.slice(0, 3).map(a => a.title).join(', ')}

        Social Data:
        - Mentions: ${socialData.mentionCount}
        - Sentiment: ${socialData.sentiment}

        Provide concise, actionable insights about the trend direction, sentiment, and significance.
      `;

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are a trend analysis expert. Provide clear, concise insights based on the data provided.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: 300,
        temperature: 0.7,
      });

      return response.choices[0]?.message?.content || 'Unable to generate insights at this time.';
    } catch (error) {
      console.error('Error generating insights:', error);
      return 'Unable to generate insights at this time.';
    }
  }

  private async generateRecommendations(
    keyword: string,
    trendData: any,
    socialData: any
  ): Promise<string[]> {
    try {
      const prompt = `
        Based on the trend data for "${keyword}", provide 3 specific, actionable recommendations:

        Current Status:
        - Hotness: ${trendData.hotness}/100
        - Trend Direction: ${this.getTrendDirection(trendData.timelineData)}
        - Sentiment: ${socialData.sentiment}
        - Social Mentions: ${socialData.mentionCount}

        Provide recommendations for content creators, marketers, or businesses interested in this keyword.
      `;

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are a strategic advisor. Provide specific, actionable recommendations.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: 200,
        temperature: 0.7,
      });

      const content = response.choices[0]?.message?.content || '';
      return content.split('\n').filter(line => line.trim().length > 0).slice(0, 3);
    } catch (error) {
      console.error('Error generating recommendations:', error);
      return [
        'Monitor the trend closely for changes',
        'Consider content opportunities around this topic',
        'Analyze competitor activities in this space',
      ];
    }
  }

  private getTrendDirection(timelineData: Array<{date: string, value: number}>): string {
    if (timelineData.length < 2) return 'stable';
    
    const recent = timelineData.slice(-3);
    const earlier = timelineData.slice(-6, -3);
    
    const recentAvg = recent.reduce((sum, item) => sum + item.value, 0) / recent.length;
    const earlierAvg = earlier.reduce((sum, item) => sum + item.value, 0) / earlier.length;
    
    if (recentAvg > earlierAvg * 1.1) return 'up';
    if (recentAvg < earlierAvg * 0.9) return 'down';
    return 'stable';
  }

  async sendDailyReports(userId: string): Promise<void> {
    try {
      const user = await db.user.findUnique({
        where: { id: userId },
        include: {
          keywords: {
            where: { isActive: true },
          },
        },
      });

      if (!user || !user.emailEnabled) {
        return;
      }

      const reports = [];
      
      for (const keyword of user.keywords) {
        try {
          const reportData = await this.generateReport(keyword.id, userId);
          reports.push(reportData);
        } catch (error) {
          console.error(`Error generating report for keyword ${keyword.term}:`, error);
        }
      }

      if (reports.length > 0) {
        await sendDailyReport(
          user.email,
          user.name || 'User',
          reports[0].keyword,
          reports[0]
        );
      }
    } catch (error) {
      console.error('Error sending daily reports:', error);
    }
  }
}
