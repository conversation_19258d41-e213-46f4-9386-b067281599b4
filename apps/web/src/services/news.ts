import axios from 'axios';

export interface NewsArticle {
  title: string;
  url: string;
  summary: string;
  publishedAt: string;
  source: string;
  imageUrl?: string;
}

export class NewsService {
  private static instance: NewsService;
  private apiKey: string;

  constructor() {
    this.apiKey = process.env.NEWS_API_KEY || '';
  }

  static getInstance(): NewsService {
    if (!NewsService.instance) {
      NewsService.instance = new NewsService();
    }
    return NewsService.instance;
  }

  async getNews(keyword: string, limit: number = 10): Promise<NewsArticle[]> {
    try {
      const response = await axios.get('https://newsapi.org/v2/everything', {
        params: {
          q: keyword,
          apiKey: this.apiKey,
          sortBy: 'publishedAt',
          pageSize: limit,
          language: 'en',
        },
      });

      return response.data.articles.map((article: any) => ({
        title: article.title,
        url: article.url,
        summary: article.description || article.content?.substring(0, 200) + '...',
        publishedAt: article.publishedAt,
        source: article.source.name,
        imageUrl: article.urlToImage,
      }));
    } catch (error) {
      console.error('Error fetching news:', error);
      return [];
    }
  }

  async getNewsSummary(keyword: string): Promise<string> {
    try {
      const articles = await this.getNews(keyword, 5);
      
      if (articles.length === 0) {
        return `No recent news found for "${keyword}".`;
      }

      const summaries = articles.map(article => 
        `${article.title}: ${article.summary}`
      ).join('\n\n');

      // In a real implementation, you'd use OpenAI to summarize
      return this.generateAISummary(summaries, keyword);
    } catch (error) {
      console.error('Error generating news summary:', error);
      return `Unable to generate news summary for "${keyword}" at this time.`;
    }
  }

  private async generateAISummary(content: string, keyword: string): Promise<string> {
    // TODO: Implement OpenAI integration for better summaries
    // For now, return a simple summary
    const sentences = content.split('.').filter(s => s.trim().length > 0);
    const topSentences = sentences.slice(0, 3).join('. ');
    
    return `Recent news about "${keyword}": ${topSentences}.`;
  }

  async getNewsCountByDay(keyword: string, days: number = 7): Promise<Array<{date: string, count: number}>> {
    try {
      const fromDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      const response = await axios.get('https://newsapi.org/v2/everything', {
        params: {
          q: keyword,
          apiKey: this.apiKey,
          from: fromDate.toISOString().split('T')[0],
          sortBy: 'publishedAt',
          pageSize: 100,
          language: 'en',
        },
      });

      const articles = response.data.articles;
      const countsByDay: { [key: string]: number } = {};

      articles.forEach((article: any) => {
        const date = new Date(article.publishedAt).toISOString().split('T')[0];
        countsByDay[date] = (countsByDay[date] || 0) + 1;
      });

      return Object.entries(countsByDay).map(([date, count]) => ({
        date,
        count,
      })).sort((a, b) => a.date.localeCompare(b.date));
    } catch (error) {
      console.error('Error fetching news count:', error);
      return [];
    }
  }
}
