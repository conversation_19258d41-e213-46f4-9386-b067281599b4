import googleTrends from 'google-trends-api';
import axios from 'axios';

export interface TrendData {
  hotness: number;
  searchVolume: number;
  relatedQueries: string[];
  timelineData: Array<{
    date: string;
    value: number;
  }>;
}

export class TrendsService {
  private static instance: TrendsService;
  
  static getInstance(): TrendsService {
    if (!TrendsService.instance) {
      TrendsService.instance = new TrendsService();
    }
    return TrendsService.instance;
  }

  async getTrendData(keyword: string): Promise<TrendData> {
    try {
      // Get Google Trends data
      const interestOverTime = await googleTrends.interestOverTime({
        keyword,
        startTime: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        geo: 'US',
      });

      const parsedData = JSON.parse(interestOverTime);
      const timelineData = parsedData.default.timelineData.map((item: any) => ({
        date: new Date(item.time * 1000).toISOString().split('T')[0],
        value: item.value[0] || 0,
      }));

      // Calculate hotness score (0-100)
      const recentValues = timelineData.slice(-7).map((item: any) => item.value);
      const avgRecent = recentValues.reduce((sum: number, val: number) => sum + val, 0) / recentValues.length;
      
      // Get related queries
      const relatedQueries = await this.getRelatedQueries(keyword);

      return {
        hotness: Math.round(avgRecent),
        searchVolume: timelineData.reduce((sum: number, item: any) => sum + item.value, 0),
        relatedQueries,
        timelineData,
      };
    } catch (error) {
      console.error('Error fetching trend data:', error);
      // Return fallback data
      return {
        hotness: 0,
        searchVolume: 0,
        relatedQueries: [],
        timelineData: [],
      };
    }
  }

  private async getRelatedQueries(keyword: string): Promise<string[]> {
    try {
      const relatedQueries = await googleTrends.relatedQueries({
        keyword,
        geo: 'US',
      });

      const parsed = JSON.parse(relatedQueries);
      return parsed.default.rankedList[0]?.rankedKeyword
        ?.slice(0, 5)
        .map((item: any) => item.query) || [];
    } catch (error) {
      console.error('Error fetching related queries:', error);
      return [];
    }
  }

  async getSuggestedKeywords(query: string): Promise<string[]> {
    try {
      // Use Google Trends autocomplete API
      const response = await axios.get(
        `https://trends.google.com/trends/api/autocomplete/${encodeURIComponent(query)}`,
        {
          params: {
            hl: 'en-US',
            tz: 240,
          },
        }
      );

      const data = response.data.replace(')]}\'', '');
      const parsed = JSON.parse(data);
      
      return parsed.default.topics
        ?.slice(0, 10)
        .map((topic: any) => topic.title) || [];
    } catch (error) {
      console.error('Error fetching suggestions:', error);
      return [];
    }
  }

  async getHotTrends(): Promise<string[]> {
    try {
      const dailyTrends = await googleTrends.dailyTrends({
        geo: 'US',
      });

      const parsed = JSON.parse(dailyTrends);
      return parsed.default.trendingSearchesDays[0]?.trendingSearches
        ?.slice(0, 10)
        .map((trend: any) => trend.title.query) || [];
    } catch (error) {
      console.error('Error fetching hot trends:', error);
      return [];
    }
  }
}
