version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: trendpulse-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: trendpulse
      POSTGRES_USER: trendpulse
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-trendpulse123}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U trendpulse"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis for job queues and caching
  redis:
    image: redis:7-alpine
    container_name: trendpulse-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Main web application
  web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: trendpulse-web
    restart: unless-stopped
    depends_on:
      - postgres
      - redis
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://trendpulse:${POSTGRES_PASSWORD:-trendpulse123}@postgres:5432/trendpulse
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - JWT_SECRET=${JWT_SECRET}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - NEWS_API_KEY=${NEWS_API_KEY}
      - TWITTER_BEARER_TOKEN=${TWITTER_BEARER_TOKEN}
      - REDDIT_CLIENT_ID=${REDDIT_CLIENT_ID}
      - REDDIT_CLIENT_SECRET=${REDDIT_CLIENT_SECRET}
      - RESEND_API_KEY=${RESEND_API_KEY}
      - FROM_EMAIL=${FROM_EMAIL}
    volumes:
      - ./apps/web:/app/web
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Job scheduler and worker
  jobs:
    build:
      context: .
      dockerfile: Dockerfile.jobs
    container_name: trendpulse-jobs
    restart: unless-stopped
    depends_on:
      - postgres
      - redis
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://trendpulse:${POSTGRES_PASSWORD:-trendpulse123}@postgres:5432/trendpulse
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - NEWS_API_KEY=${NEWS_API_KEY}
      - TWITTER_BEARER_TOKEN=${TWITTER_BEARER_TOKEN}
      - REDDIT_CLIENT_ID=${REDDIT_CLIENT_ID}
      - REDDIT_CLIENT_SECRET=${REDDIT_CLIENT_SECRET}
      - RESEND_API_KEY=${RESEND_API_KEY}
      - FROM_EMAIL=${FROM_EMAIL}
    volumes:
      - ./jobs:/app/jobs

volumes:
  postgres_data:
  redis_data:
