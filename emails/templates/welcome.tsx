import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface WelcomeEmailProps {
  name: string;
}

export const WelcomeEmail = ({ name = 'User' }: WelcomeEmailProps) => (
  <Html>
    <Head />
    <Preview>Welcome to TrendPulse - Your AI-powered trend analysis starts here!</Preview>
    <Body style={main}>
      <Container style={container}>
        <Section style={header}>
          <Heading style={heading}>TrendPulse</Heading>
        </Section>

        <Section style={content}>
          <Heading style={h1}>Welcome to TrendPulse, {name}!</Heading>
          
          <Text style={text}>
            Thank you for joining TrendPulse! You're now part of a community that stays ahead of the curve with AI-powered trend analysis.
          </Text>

          <Text style={text}>
            Here's what you can do with TrendPulse:
          </Text>

          <Section style={featureSection}>
            <div style={feature}>
              <Text style={featureTitle}>📊 Track Keywords</Text>
              <Text style={featureDescription}>
                Add keywords you care about and monitor their trending status in real-time.
              </Text>
            </div>

            <div style={feature}>
              <Text style={featureTitle}>📈 Daily Reports</Text>
              <Text style={featureDescription}>
                Receive comprehensive daily reports with trends, news, and insights delivered to your inbox.
              </Text>
            </div>

            <div style={feature}>
              <Text style={featureTitle}>🤖 AI Insights</Text>
              <Text style={featureDescription}>
                Get AI-powered analysis and recommendations based on trend data and social sentiment.
              </Text>
            </div>

            <div style={feature}>
              <Text style={featureTitle}>🎯 Smart Recommendations</Text>
              <Text style={featureDescription}>
                Receive actionable recommendations for content creation and strategic decisions.
              </Text>
            </div>
          </Section>

          <Text style={text}>
            Ready to get started? Click the button below to set up your first keywords and configure your preferences.
          </Text>

          <Section style={ctaSection}>
            <Link href={`${process.env.NEXT_PUBLIC_APP_URL}/dashboard`} style={button}>
              Get Started
            </Link>
          </Section>

          <Text style={text}>
            If you have any questions, feel free to reach out to our support team. We're here to help you make the most of TrendPulse!
          </Text>

          <Text style={text}>
            Best regards,<br />
            The TrendPulse Team
          </Text>
        </Section>

        <Section style={footer}>
          <Text style={footerText}>
            You're receiving this email because you signed up for TrendPulse. 
            <Link href={`${process.env.NEXT_PUBLIC_APP_URL}/settings`} style={link}>
              Manage your preferences
            </Link> | 
            <Link href={`${process.env.NEXT_PUBLIC_APP_URL}/unsubscribe`} style={link}>
              Unsubscribe
            </Link>
          </Text>
        </Section>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  maxWidth: '600px',
};

const header = {
  backgroundColor: '#ffffff',
  borderRadius: '8px 8px 0 0',
  padding: '24px',
  borderBottom: '1px solid #e6e6e6',
  textAlign: 'center' as const,
};

const heading = {
  fontSize: '28px',
  fontWeight: '700',
  color: '#4f46e5',
  margin: '0',
};

const content = {
  backgroundColor: '#ffffff',
  padding: '32px',
  borderRadius: '0 0 8px 8px',
};

const h1 = {
  color: '#1f2937',
  fontSize: '24px',
  fontWeight: '600',
  margin: '0 0 24px 0',
  textAlign: 'center' as const,
};

const text = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '1.6',
  margin: '0 0 16px 0',
};

const featureSection = {
  margin: '24px 0',
};

const feature = {
  backgroundColor: '#f8fafc',
  padding: '20px',
  borderRadius: '8px',
  margin: '16px 0',
};

const featureTitle = {
  color: '#1f2937',
  fontSize: '18px',
  fontWeight: '600',
  margin: '0 0 8px 0',
};

const featureDescription = {
  color: '#6b7280',
  fontSize: '14px',
  lineHeight: '1.5',
  margin: '0',
};

const ctaSection = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  backgroundColor: '#4f46e5',
  color: '#ffffff',
  padding: '16px 32px',
  borderRadius: '8px',
  textDecoration: 'none',
  fontWeight: '600',
  fontSize: '16px',
  display: 'inline-block',
};

const footer = {
  backgroundColor: '#f8fafc',
  padding: '20px',
  borderTop: '1px solid #e6e6e6',
  borderRadius: '0 0 8px 8px',
  textAlign: 'center' as const,
};

const footerText = {
  color: '#6b7280',
  fontSize: '12px',
  margin: '0',
  lineHeight: '1.4',
};

const link = {
  color: '#4f46e5',
  textDecoration: 'none',
};

export default WelcomeEmail;
