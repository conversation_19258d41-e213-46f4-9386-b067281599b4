import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
  Row,
  Column,
} from '@react-email/components';
import * as React from 'react';

interface DailyReportEmailProps {
  userName: string;
  keyword: string;
  reportData: {
    hotness: number;
    sentiment: number;
    insights: string;
    news: Array<{
      title: string;
      url: string;
      summary: string;
      source: string;
    }>;
    recommendations: string[];
  };
}

export const DailyReportEmail = ({
  userName = 'User',
  keyword = 'AI',
  reportData = {
    hotness: 75,
    sentiment: 0.3,
    insights: 'This keyword is showing strong growth momentum.',
    news: [
      {
        title: 'Latest AI breakthrough announced',
        url: 'https://example.com',
        summary: 'Major tech company announces new AI model.',
        source: 'Tech News',
      },
    ],
    recommendations: [
      'Consider creating content about this topic',
      'Monitor for trending subtopics',
      'Engage with related communities',
    ],
  },
}: DailyReportEmailProps) => (
  <Html>
    <Head />
    <Preview>Your daily trend report for {keyword} is ready!</Preview>
    <Body style={main}>
      <Container style={container}>
        <Section style={header}>
          <Row>
            <Column>
              <Heading style={heading}>TrendPulse</Heading>
            </Column>
          </Row>
        </Section>

        <Section style={content}>
          <Heading style={h1}>Daily Report: {keyword}</Heading>
          <Text style={text}>Hi {userName},</Text>
          <Text style={text}>
            Here's your daily trend report for "{keyword}":
          </Text>

          {/* Hotness Score */}
          <Section style={metricSection}>
            <Row>
              <Column style={metricColumn}>
                <Text style={metricLabel}>Hotness Score</Text>
                <Text style={metricValue}>{reportData.hotness}/100</Text>
              </Column>
              <Column style={metricColumn}>
                <Text style={metricLabel}>Sentiment</Text>
                <Text style={metricValue}>
                  {reportData.sentiment > 0 ? '📈 Positive' : 
                   reportData.sentiment < 0 ? '📉 Negative' : '➡️ Neutral'}
                </Text>
              </Column>
            </Row>
          </Section>

          {/* Insights */}
          <Section style={insightsSection}>
            <Heading style={h2}>Key Insights</Heading>
            <Text style={text}>{reportData.insights}</Text>
          </Section>

          {/* Top News */}
          <Section style={newsSection}>
            <Heading style={h2}>Top News</Heading>
            {reportData.news.map((article, index) => (
              <div key={index} style={newsItem}>
                <Link href={article.url} style={newsTitle}>
                  {article.title}
                </Link>
                <Text style={newsSource}>{article.source}</Text>
                <Text style={newsSummary}>{article.summary}</Text>
              </div>
            ))}
          </Section>

          {/* Recommendations */}
          <Section style={recommendationsSection}>
            <Heading style={h2}>Recommendations</Heading>
            {reportData.recommendations.map((rec, index) => (
              <Text key={index} style={recommendationItem}>
                • {rec}
              </Text>
            ))}
          </Section>

          {/* CTA */}
          <Section style={ctaSection}>
            <Link href={`${process.env.NEXT_PUBLIC_APP_URL}/dashboard`} style={button}>
              View Full Report
            </Link>
          </Section>
        </Section>

        <Section style={footer}>
          <Text style={footerText}>
            This report was generated by TrendPulse. 
            <Link href={`${process.env.NEXT_PUBLIC_APP_URL}/settings`} style={link}>
              Manage your preferences
            </Link>
          </Text>
        </Section>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  maxWidth: '600px',
};

const header = {
  backgroundColor: '#ffffff',
  borderRadius: '8px 8px 0 0',
  padding: '24px',
  borderBottom: '1px solid #e6e6e6',
};

const heading = {
  fontSize: '24px',
  fontWeight: '600',
  color: '#4f46e5',
  margin: '0',
};

const content = {
  backgroundColor: '#ffffff',
  padding: '24px',
  borderRadius: '0 0 8px 8px',
};

const h1 = {
  color: '#1f2937',
  fontSize: '20px',
  fontWeight: '600',
  margin: '0 0 16px 0',
};

const h2 = {
  color: '#1f2937',
  fontSize: '16px',
  fontWeight: '600',
  margin: '24px 0 12px 0',
};

const text = {
  color: '#374151',
  fontSize: '14px',
  lineHeight: '1.6',
  margin: '0 0 16px 0',
};

const metricSection = {
  backgroundColor: '#f8fafc',
  padding: '20px',
  borderRadius: '8px',
  margin: '20px 0',
};

const metricColumn = {
  textAlign: 'center' as const,
};

const metricLabel = {
  color: '#6b7280',
  fontSize: '12px',
  fontWeight: '500',
  margin: '0 0 4px 0',
};

const metricValue = {
  color: '#1f2937',
  fontSize: '24px',
  fontWeight: '700',
  margin: '0',
};

const insightsSection = {
  margin: '24px 0',
};

const newsSection = {
  margin: '24px 0',
};

const newsItem = {
  backgroundColor: '#f8fafc',
  padding: '16px',
  borderRadius: '6px',
  margin: '12px 0',
};

const newsTitle = {
  color: '#4f46e5',
  fontSize: '14px',
  fontWeight: '600',
  textDecoration: 'none',
  display: 'block',
  margin: '0 0 4px 0',
};

const newsSource = {
  color: '#6b7280',
  fontSize: '12px',
  margin: '0 0 8px 0',
};

const newsSummary = {
  color: '#374151',
  fontSize: '13px',
  lineHeight: '1.4',
  margin: '0',
};

const recommendationsSection = {
  margin: '24px 0',
};

const recommendationItem = {
  color: '#374151',
  fontSize: '14px',
  margin: '8px 0',
};

const ctaSection = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  backgroundColor: '#4f46e5',
  color: '#ffffff',
  padding: '12px 24px',
  borderRadius: '6px',
  textDecoration: 'none',
  fontWeight: '600',
  fontSize: '14px',
  display: 'inline-block',
};

const footer = {
  backgroundColor: '#f8fafc',
  padding: '16px',
  borderTop: '1px solid #e6e6e6',
  borderRadius: '0 0 8px 8px',
  textAlign: 'center' as const,
};

const footerText = {
  color: '#6b7280',
  fontSize: '12px',
  margin: '0',
};

const link = {
  color: '#4f46e5',
  textDecoration: 'none',
};

export default DailyReportEmail;
