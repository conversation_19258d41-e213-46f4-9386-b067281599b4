# TrendPulse

A comprehensive web application for tracking keyword trends and generating AI-powered daily reports. Built with Next.js 14, PostgreSQL, Redis, and integrated with multiple APIs for trend analysis.

## Features

- **Keyword Management**: Add and track multiple keywords with real-time trend monitoring
- **Daily Reports**: Automated daily reports with AI-powered insights and recommendations
- **Multi-source Data**: Integration with Google Trends, News API, Twitter, and Reddit
- **Email Notifications**: Beautiful HTML email reports delivered to your inbox
- **Real-time Dashboard**: Interactive dashboard with charts and trend visualization
- **Dark/Light Mode**: Responsive design with theme switching
- **Job Scheduling**: Background jobs for data fetching and report generation

## Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM, PostgreSQL
- **Jobs**: BullMQ, Redis, node-cron
- **Email**: React Email, Resend
- **AI**: OpenAI GPT-4o for insights and recommendations
- **APIs**: Google Trends, News API, Twitter API, Reddit API
- **Deployment**: <PERSON><PERSON>, <PERSON>er Compose

## Quick Start

### Prerequisites

- Node.js 18+
- Docker and Docker Compose
- pnpm (recommended) or npm

### 1. Clone the repository

```bash
git clone https://github.com/yourusername/trendpulse.git
cd trendpulse
```

### 2. Environment Setup

Copy the environment variables template:

```bash
cp .env.example .env
```

Edit `.env` with your API keys and configuration:

```env
# Required API Keys
OPENAI_API_KEY=your-openai-api-key
NEWS_API_KEY=your-news-api-key
RESEND_API_KEY=your-resend-api-key

# Optional but recommended
TWITTER_BEARER_TOKEN=your-twitter-bearer-token
REDDIT_CLIENT_ID=your-reddit-client-id
REDDIT_CLIENT_SECRET=your-reddit-client-secret
```

### 3. Start with Docker (Recommended)

```bash
# Start all services
docker-compose up -d

# Run database migrations
docker-compose exec web pnpm --filter web db:migrate

# View logs
docker-compose logs -f web
```

The application will be available at `http://localhost:3000`

### 4. Development Setup (Alternative)

If you prefer to run without Docker:

```bash
# Install dependencies
pnpm install

# Start PostgreSQL and Redis
docker-compose up -d postgres redis

# Run database migrations
pnpm --filter web db:migrate

# Start the development server
pnpm dev

# In another terminal, start the job scheduler
pnpm --filter jobs dev
```

## API Keys Setup

### Required APIs

1. **OpenAI API** - For AI insights and recommendations
   - Visit: https://platform.openai.com/api-keys
   - Create an API key and add to `OPENAI_API_KEY`

2. **News API** - For news data
   - Visit: https://newsapi.org/register
   - Get your API key and add to `NEWS_API_KEY`

3. **Resend** - For email sending
   - Visit: https://resend.com/api-keys
   - Create API key and add to `RESEND_API_KEY`

### Optional APIs (Enhanced Features)

4. **Twitter API** - For social media data
   - Visit: https://developer.twitter.com/en/portal/dashboard
   - Create an app and get Bearer Token
   - Add to `TWITTER_BEARER_TOKEN`

5. **Reddit API** - For Reddit data
   - Visit: https://www.reddit.com/prefs/apps
   - Create an app and get client ID/secret
   - Add to `REDDIT_CLIENT_ID` and `REDDIT_CLIENT_SECRET`

## Project Structure

```
TrendPulse/
├── apps/web/                 # Next.js web application
│   ├── src/
│   │   ├── app/             # App router pages
│   │   ├── components/      # React components
│   │   ├── lib/             # Utility functions
│   │   └── services/        # API services
│   └── prisma/              # Database schema
├── jobs/                    # Background job scheduler
├── emails/                  # Email templates
├── packages/                # Shared packages
├── tests/                   # Test files
└── docker-compose.yml       # Docker configuration
```

## Key Features

### Dashboard
- Keyword overview with hotness scores
- Trend indicators and progress bars
- Quick actions for managing keywords

### Reports
- Historical report viewing
- Filterable by date and keyword
- Export functionality

### Settings
- User preferences
- Email notification settings
- Timezone configuration

### Background Jobs
- Daily report generation at 10 AM user time
- Trend data fetching every 4 hours
- Email delivery queue

## Development

### Database Management

```bash
# Run migrations
pnpm --filter web db:migrate

# Reset database
pnpm --filter web db:reset

# Open Prisma Studio
pnpm --filter web db:studio
```

### Testing

```bash
# Run unit tests
pnpm test

# Run E2E tests
pnpm test:e2e

# Run type checking
pnpm type-check
```

### Email Development

```bash
# Start email development server
pnpm --filter emails dev
```

## Deployment

### Production with Docker

```bash
# Build and start production containers
docker-compose -f docker-compose.yml up -d

# Run migrations
docker-compose exec web pnpm --filter web db:migrate
```

### Manual Deployment

1. Set production environment variables
2. Build the application: `pnpm build`
3. Start the application: `pnpm start`
4. Ensure PostgreSQL and Redis are running
5. Start the job scheduler: `pnpm --filter jobs start`

### Cloud Deployment

The application is configured for deployment on:
- **Vercel** (web app)
- **Railway** or **Render** (database)
- **Upstash** (Redis)

## Configuration

### Environment Variables

All configuration is done through environment variables. See `.env.example` for the complete list.

### Scheduling

Reports are generated daily at 10 AM in the user's timezone. This is configurable in the job scheduler.

### Rate Limiting

API rate limiting is implemented to prevent abuse:
- 100 requests per minute per IP
- 1000 requests per hour per user

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Ensure PostgreSQL is running
   - Check DATABASE_URL in .env

2. **Redis Connection Error**
   - Ensure Redis is running
   - Check REDIS_HOST and REDIS_PORT

3. **API Key Issues**
   - Verify all required API keys are set
   - Check API key permissions and quotas

4. **Email Not Sending**
   - Verify RESEND_API_KEY
   - Check FROM_EMAIL domain verification

### Logs

```bash
# View application logs
docker-compose logs -f web

# View job scheduler logs
docker-compose logs -f jobs
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License. See [LICENSE](LICENSE) for details.

## Support

For support, please open an issue on GitHub or contact the development team.
