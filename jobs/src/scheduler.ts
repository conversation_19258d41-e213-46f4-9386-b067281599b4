import cron from 'node-cron';
import { Queue, Worker } from 'bullmq';
import { Redis } from 'ioredis';
import pino from 'pino';
import dotenv from 'dotenv';

dotenv.config();

const logger = pino({
  level: process.env.LOG_LEVEL || 'info',
  transport: {
    target: 'pino-pretty',
  },
});

const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
});

// Create job queues
const reportQueue = new Queue('report-generation', { connection: redis });
const emailQueue = new Queue('email-sending', { connection: redis });

// Daily report generation scheduler
cron.schedule('0 10 * * *', async () => {
  logger.info('Starting daily report generation job');
  
  try {
    // Add jobs for all active users
    await reportQueue.add('generate-daily-reports', {
      timestamp: new Date().toISOString(),
    });
    
    logger.info('Daily report generation job queued successfully');
  } catch (error) {
    logger.error('Failed to queue daily report generation job:', error);
  }
});

// Trend data fetching scheduler (every 4 hours)
cron.schedule('0 */4 * * *', async () => {
  logger.info('Starting trend data fetch job');
  
  try {
    await reportQueue.add('fetch-trend-data', {
      timestamp: new Date().toISOString(),
    });
    
    logger.info('Trend data fetch job queued successfully');
  } catch (error) {
    logger.error('Failed to queue trend data fetch job:', error);
  }
});

// Report generation worker
const reportWorker = new Worker('report-generation', async (job) => {
  logger.info(`Processing job: ${job.name}`, { jobId: job.id });
  
  try {
    switch (job.name) {
      case 'generate-daily-reports':
        await processDailyReports();
        break;
      case 'fetch-trend-data':
        await fetchTrendData();
        break;
      case 'generate-single-report':
        await generateSingleReport(job.data);
        break;
      default:
        logger.warn(`Unknown job type: ${job.name}`);
    }
  } catch (error) {
    logger.error(`Job failed: ${job.name}`, { jobId: job.id, error });
    throw error;
  }
}, { connection: redis });

// Email sending worker
const emailWorker = new Worker('email-sending', async (job) => {
  logger.info(`Processing email job: ${job.name}`, { jobId: job.id });
  
  try {
    switch (job.name) {
      case 'send-daily-report':
        await sendDailyReportEmail(job.data);
        break;
      case 'send-welcome-email':
        await sendWelcomeEmail(job.data);
        break;
      default:
        logger.warn(`Unknown email job type: ${job.name}`);
    }
  } catch (error) {
    logger.error(`Email job failed: ${job.name}`, { jobId: job.id, error });
    throw error;
  }
}, { connection: redis });

// Job processing functions
async function processDailyReports() {
  logger.info('Processing daily reports for all users');
  
  // This would typically connect to your database
  // For now, we'll simulate the process
  const users = await getActiveUsers();
  
  for (const user of users) {
    try {
      await reportQueue.add('generate-single-report', {
        userId: user.id,
        userEmail: user.email,
        userName: user.name,
        timezone: user.timezone,
      });
    } catch (error) {
      logger.error(`Failed to queue report for user ${user.id}:`, error);
    }
  }
}

async function fetchTrendData() {
  logger.info('Fetching trend data for all keywords');
  
  // This would fetch all active keywords and update their trend data
  const keywords = await getActiveKeywords();
  
  for (const keyword of keywords) {
    try {
      // Simulate trend data fetching
      logger.info(`Fetching trend data for keyword: ${keyword.term}`);
      
      // In a real implementation, this would call your trend service
      // await trendsService.updateTrendData(keyword.id);
    } catch (error) {
      logger.error(`Failed to fetch trend data for keyword ${keyword.term}:`, error);
    }
  }
}

async function generateSingleReport(data: any) {
  logger.info(`Generating report for user: ${data.userId}`);
  
  // This would call your report generation service
  // const reportData = await reportGenerator.generateReport(data.userId);
  
  // Queue email sending
  await emailQueue.add('send-daily-report', {
    userEmail: data.userEmail,
    userName: data.userName,
    reportData: {}, // This would be the actual report data
  });
}

async function sendDailyReportEmail(data: any) {
  logger.info(`Sending daily report email to: ${data.userEmail}`);
  
  // This would call your email service
  // await emailService.sendDailyReport(data.userEmail, data.userName, data.reportData);
}

async function sendWelcomeEmail(data: any) {
  logger.info(`Sending welcome email to: ${data.userEmail}`);
  
  // This would call your email service
  // await emailService.sendWelcomeEmail(data.userEmail, data.userName);
}

// Mock database functions (replace with actual database calls)
async function getActiveUsers(): Promise<Array<{
  id: string;
  email: string;
  name: string;
  timezone: string;
}>> {
  // Mock data - replace with actual database query
  return [
    {
      id: '1',
      email: '<EMAIL>',
      name: 'Test User',
      timezone: 'UTC',
    },
  ];
}

async function getActiveKeywords(): Promise<Array<{
  id: string;
  term: string;
  userId: string;
}>> {
  // Mock data - replace with actual database query
  return [
    {
      id: '1',
      term: 'AI',
      userId: '1',
    },
  ];
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, shutting down gracefully');
  
  await reportWorker.close();
  await emailWorker.close();
  await reportQueue.close();
  await emailQueue.close();
  await redis.quit();
  
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('Received SIGINT, shutting down gracefully');
  
  await reportWorker.close();
  await emailWorker.close();
  await reportQueue.close();
  await emailQueue.close();
  await redis.quit();
  
  process.exit(0);
});

logger.info('Job scheduler started');
