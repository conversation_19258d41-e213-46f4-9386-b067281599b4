{"name": "trendpulse", "version": "1.0.0", "private": true, "scripts": {"dev": "pnpm --filter web dev", "build": "pnpm --filter web build", "start": "pnpm --filter web start", "lint": "eslint . --ext .ts,.tsx --fix", "test": "vitest", "test:e2e": "playwright test", "db:push": "pnpm --filter web db:push", "db:migrate": "pnpm --filter web db:migrate", "db:generate": "pnpm --filter web db:generate", "type-check": "pnpm --filter web type-check", "format": "prettier --write .", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "devDependencies": {"@playwright/test": "^1.40.0", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.55.0", "eslint-config-next": "^14.0.0", "eslint-config-prettier": "^9.1.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.0", "typescript": "^5.3.0", "vitest": "^1.0.0"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"]}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.12.0"}